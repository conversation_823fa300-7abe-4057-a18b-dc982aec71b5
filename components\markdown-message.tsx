import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import type { FC } from "react";

interface MarkdownMessageProps {
  content: string;
}

export const MarkdownMessage: FC<MarkdownMessageProps> = ({ content }) => {
  return (
    <div className="max-w-full text-gray-800">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          code({ node, inline, className, children, ...props }: any) {
            if (!inline) {
              return (
                <pre className="bg-[#f5f5f5] rounded-lg p-4 overflow-x-auto my-4 shadow-sm">
                  <code className={`${className} language-none`} {...props}>
                    {children}
                  </code>
                </pre>
              );
            } else {
              return (
                <code className="bg-[#e3e8ee] text-[#c7254e] rounded px-1.5 py-0.5 font-mono">
                  {children}
                </code>
              );
            }
          },
          h1({ children }) {
            return (
              <h1 className="text-3xl font-bold text-gray-900 mt-8 mb-6 border-b-2 border-gray-200 pb-2">
                {children}
              </h1>
            );
          },
          h2({ children }) {
            return (
              <h2 className="text-2xl font-semibold text-gray-900 mt-6 mb-4 border-b border-gray-200 pb-1">
                {children}
              </h2>
            );
          },
          h3({ children }) {
            return (
              <h3 className="text-xl font-semibold text-gray-900 mt-5 mb-3">
                {children}
              </h3>
            );
          },
          h4({ children }) {
            return (
              <h4 className="text-lg font-semibold text-gray-900 mt-4 mb-2">
                {children}
              </h4>
            );
          },
          table({ children }) {
            return (
              <div className="overflow-x-auto my-4 shadow-sm rounded-lg flex justify-center">
                <table className="min-w-full bg-white border border-gray-200">
                  {children}
                </table>
              </div>
            );
          },
          th({ children }) {
            return (
              <th className="bg-gray-100 border-b px-4 py-2 text-left text-sm font-semibold text-gray-700">
                {children}
              </th>
            );
          },
          td({ children }) {
            return (
              <td className="border-b px-4 py-2 text-sm text-gray-800">
                {children}
              </td>
            );
          },
          blockquote({ children }) {
            return (
              <blockquote className="border-l-4 border-blue-300 bg-blue-50 italic pl-4 pr-3 py-3 my-4 rounded-md">
                {children}
              </blockquote>
            );
          },
          ul({ children }) {
            return (
              <ul className="list-disc list-inside my-4 space-y-1 pl-4">
                {children}
              </ul>
            );
          },
          ol({ children }) {
            return (
              <ol className="list-decimal list-inside my-4 space-y-1 pl-4">
                {children}
              </ol>
            );
          },
          li({ children }) {
            return (
              <li className="text-gray-800 leading-relaxed">{children}</li>
            );
          },
          p({ children }) {
            return (
              <p className="my-3 leading-relaxed text-gray-800">{children}</p>
            );
          },
          strong({ children }) {
            return (
              <strong className="font-semibold text-gray-900">{children}</strong>
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
