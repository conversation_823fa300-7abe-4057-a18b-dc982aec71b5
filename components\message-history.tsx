"use client"

import { useSelector } from "react-redux"
import type { RootState } from "@/store"
import type { Message } from "@/types"
import { MarkdownMessage } from "./markdown-message"
import { Use<PERSON>, <PERSON><PERSON> } from "lucide-react"
import { ThinkingProcess } from "./thinking-process"
import { CompletedStepsAccordion } from "./completed-steps-accordion"

interface MessageHistoryProps {
  messages: Message[]
}

export function MessageHistory({ messages }: MessageHistoryProps) {
  // Get WebSocket state for real-time thinking process
  const finalResponse = useSelector((state: RootState) => state.chat.finalResponse)

  // Helper function to check if a message has active thinking steps
  const hasActiveThinking = (message: Message) => {
    return message.thinkingSteps && Object.keys(message.thinkingSteps).length > 0
  }

  const hasInProgressSteps = (message: Message) => {
    return message.thinkingSteps && Object.values(message.thinkingSteps).some(step => step.status === 'in-progress')
  }

  if (!messages || messages.length === 0) {
    return null
  }

  // Get the last user message for context
  const lastUserMessage = messages.findLast((m) => m.type === "user")?.content || "Processing..."

  return (
    <div className="space-y-5">
      {messages.map((message) => (
        <div key={message.id} className="flex gap-6 justify-start">

          {/* Message content - always left aligned */}
          <div
            className={`w-[90%] rounded-2xl ${message.type === "user" ? "" : "bg-transparent px-4 py-3"}`}
          >
            {message.type === "user" ? (
              <p className="text-[1.5rem] font-medium leading-relaxed text-gray-900">{message.content}</p>
            ) : (
              <div className="space-y-3">
                {/* Show WebSocket thinking process if active, otherwise fall back to message thinking state */}
                {hasActiveThinking(message) ? (
                  <ThinkingProcess
                    currentQuery={lastUserMessage}
                    messageId={message.id}
                  />
                ) : message.thinkingState?.status === "processing" && message.thinkingState.steps ? (
                  <ThinkingProcess
                    currentQuery={
                      messages.findLast((m) => m.type === "user" && m.timestamp < message.timestamp)?.content ||
                      "Processing..."
                    }
                    steps={message.thinkingState.steps}
                    messageId={message.id}
                  />
                ) : null}

                {/* Show final response from WebSocket or message content */}
                {finalResponse && !hasInProgressSteps(message) ? (
                  <div className="space-y-3">
                    <div className="max-w-none mr-4">
                      <MarkdownMessage content={finalResponse.content} />
                    </div>
                  </div>
                ) : message.thinkingState?.status === "completed" ? (
                  <div className="space-y-3">
                    {/* Sources first, then content */}
                    {message.thinkingState.steps && message.thinkingState.steps.length > 0 && (
                      <div>
                        <CompletedStepsAccordion steps={message.thinkingState.steps} sources={message.sources} />
                      </div>
                    )}

                    {/* Response content below sources */}
                    {message.responseTitle && (
                      <h3 className="text-xl font-semibold text-gray-900 leading-relaxed pt-4">
                        {message.responseTitle}
                      </h3>
                    )}
                    <div className="max-w-none mr-4">
                      <MarkdownMessage content={message.content} />
                    </div>
                  </div>
                ) : !message.thinkingState && message.content ? (
                  <div className="space-y-6">
                    {/* Sources first for non-thinking messages too */}
                    {message.sources && message.sources.length > 0 && (
                      <div className="pb-4 border-b border-gray-100">
                        <h5 className="text-sm font-semibold text-gray-700 mb-3">Sources:</h5>
                        <ul className="space-y-2">
                          {message.sources.map((src) => (
                            <li key={src.id} className="text-sm text-blue-600 hover:underline">
                              <a href={src.url || "#"} target="_blank" rel="noopener noreferrer">
                                {src.name}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Content below sources */}
                    {message.responseTitle && (
                      <h3 className="text-xl font-semibold text-gray-900 leading-relaxed">{message.responseTitle}</h3>
                    )}
                    <div className="max-w-none mr-4">
                      <MarkdownMessage content={message.content} />
                    </div>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
