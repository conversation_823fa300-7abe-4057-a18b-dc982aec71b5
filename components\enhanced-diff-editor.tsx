'use client'

import React, { useState, useEffect, useMemo, useCallback, useRef, memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { diffWords } from 'diff'
import TurndownService from 'turndown'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Button } from '@/components/ui/button'
import { Edit3, Save, X, Eye, Bold, Italic, List, ListOrdered, Type } from 'lucide-react'

interface EnhancedDiffEditorProps {
  initialValue: string
  onSave?: (content: string) => void
  className?: string
}

interface DiffPart {
  value: string
  added?: boolean
  removed?: boolean
}

const turndownService = new TurndownService({
  headingStyle: 'atx',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced'
})

const EnhancedDiffEditorComponent = ({
  initialValue,
  onSave,
  className = ''
}: EnhancedDiffEditorProps) => {
  const [isEditMode, setIsEditMode] = useState(false)
  const [editedContent, setEditedContent] = useState('')
  const [originalMarkdown, setOriginalMarkdown] = useState(initialValue)
  const [debouncedContent, setDebouncedContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const editorRef = useRef<HTMLDivElement>(null)

  // Initialize content
  useEffect(() => {
    setOriginalMarkdown(initialValue || '')
  }, [initialValue])

  // Debounce content changes for diff calculation
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedContent(editedContent)
    }, 300)

    return () => clearTimeout(timer)
  }, [editedContent])

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    return () => {
      // Clear all state when component unmounts
      setEditedContent('')
      setDebouncedContent('')
      setIsLoading(false)
    }
  }, [])

  // Convert markdown to HTML for contentEditable with error handling
  const markdownToHtml = useCallback((markdown: string) => {
    try {
      if (!markdown || typeof markdown !== 'string') {
        return '<p>No content</p>'
      }

      // Escape HTML to prevent XSS
      const escaped = markdown
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')

      // Simple markdown to HTML conversion with performance optimizations
      return escaped
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/gim, '<em>$1</em>')
        .replace(/`(.*?)`/gim, '<code>$1</code>')
        .replace(/\n\n/gim, '</p><p>')
        .replace(/\n/gim, '<br>')
        .replace(/^(.*)$/gim, '<p>$1</p>')
        .replace(/<p><\/p>/gim, '')
        .replace(/<p><br><\/p>/gim, '<p></p>') // Clean up empty paragraphs with breaks
    } catch (error) {
      console.error('Error converting markdown to HTML:', error)
      return `<p>Error processing content: ${markdown}</p>`
    }
  }, [])

  // Convert HTML back to markdown with error handling
  const htmlToMarkdown = useCallback((html: string) => {
    try {
      if (!html || typeof html !== 'string') {
        return ''
      }

      // Clean up common contentEditable artifacts
      const cleanedHtml = html
        .replace(/<div><br><\/div>/g, '\n') // Empty divs with breaks
        .replace(/<div>/g, '\n') // Div tags
        .replace(/<\/div>/g, '') // Closing div tags
        .replace(/<br\s*\/?>/g, '\n') // Break tags
        .replace(/&nbsp;/g, ' ') // Non-breaking spaces
        .trim()

      return turndownService.turndown(cleanedHtml)
    } catch (error) {
      console.error('Error converting HTML to markdown:', error)
      return html // Return original HTML if conversion fails
    }
  }, [])

  // Formatting functions
  const formatText = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value)
    if (editorRef.current) {
      setEditedContent(editorRef.current.innerHTML)
    }
  }, [])

  const handleEditorInput = useCallback(() => {
    if (editorRef.current) {
      try {
        const content = editorRef.current.innerHTML
        // Prevent excessive updates for very large content
        if (content.length > 50000) { // 50KB limit
          console.warn('Content is very large, diff preview may be limited')
        }
        setEditedContent(content)
      } catch (error) {
        console.error('Error handling editor input:', error)
      }
    }
  }, [])



  // Calculate diff between original and edited content with performance optimizations
  const diffParts = useMemo(() => {
    if (!debouncedContent || !originalMarkdown) return []

    try {
      const editedMarkdown = htmlToMarkdown(debouncedContent)

      // Skip diff calculation if content is identical
      if (editedMarkdown === originalMarkdown) {
        return []
      }

      // Limit diff calculation for very large content to prevent performance issues
      const maxLength = 10000 // 10KB limit
      if (originalMarkdown.length > maxLength || editedMarkdown.length > maxLength) {
        return [{
          value: 'Content too large for diff preview. Changes will be saved correctly.',
          added: false,
          removed: false
        }]
      }

      return diffWords(originalMarkdown, editedMarkdown)
    } catch (error) {
      console.error('Error calculating diff:', error)
      return [{
        value: 'Error calculating diff preview. Content will still be saved correctly.',
        added: false,
        removed: false
      }]
    }
  }, [originalMarkdown, debouncedContent, htmlToMarkdown])

  const handleEditClick = useCallback(() => {
    try {
      setIsLoading(true)
      const htmlContent = markdownToHtml(originalMarkdown || '')
      setEditedContent(htmlContent)
      setIsEditMode(true)

      // Set the editor content after a brief delay to ensure the ref is available
      setTimeout(() => {
        if (editorRef.current) {
          try {
            editorRef.current.innerHTML = htmlContent
            // Focus the editor for better UX
            editorRef.current.focus()
          } catch (error) {
            console.error('Error setting editor content:', error)
          }
        }
        setIsLoading(false)
      }, 10)
    } catch (error) {
      console.error('Error entering edit mode:', error)
      setIsLoading(false)
    }
  }, [originalMarkdown, markdownToHtml])

  const handleSave = useCallback(() => {
    try {
      setIsLoading(true)
      const finalMarkdown = htmlToMarkdown(editedContent || '')
      setOriginalMarkdown(finalMarkdown)
      onSave?.(finalMarkdown)
      setIsEditMode(false)
      setEditedContent('') // Clear edited content to free memory
      setDebouncedContent('') // Clear debounced content
      console.log('Saved markdown:', finalMarkdown)
      setIsLoading(false)
    } catch (error) {
      console.error('Error saving content:', error)
      // Still try to exit edit mode even if save fails
      setIsEditMode(false)
      setIsLoading(false)
    }
  }, [editedContent, htmlToMarkdown, onSave])

  const handleCancel = useCallback(() => {
    try {
      setEditedContent('') // Clear edited content to free memory
      setIsEditMode(false)
      setDebouncedContent('') // Clear debounced content
    } catch (error) {
      console.error('Error canceling edit:', error)
      // Force exit edit mode
      setIsEditMode(false)
    }
  }, [])

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    try {
      // Ctrl+S or Cmd+S to save
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        handleSave()
        return
      }

      // Escape to cancel
      if (e.key === 'Escape') {
        e.preventDefault()
        handleCancel()
        return
      }

      // Ctrl+B or Cmd+B for bold
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault()
        formatText('bold')
        return
      }

      // Ctrl+I or Cmd+I for italic
      if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
        e.preventDefault()
        formatText('italic')
        return
      }
    } catch (error) {
      console.error('Error handling keyboard shortcut:', error)
    }
  }, [handleSave, handleCancel, formatText])

  const renderDiffPreview = useCallback(() => {
    if (diffParts.length === 0) {
      return (
        <div className="text-gray-500 italic p-4">
          Start editing to see changes...
        </div>
      )
    }

    // Limit the number of diff parts rendered to prevent performance issues
    const maxDiffParts = 1000
    const partsToRender = diffParts.length > maxDiffParts
      ? diffParts.slice(0, maxDiffParts)
      : diffParts

    return (
      <div className="prose prose-sm max-w-none">
        {partsToRender.map((part: DiffPart, index: number) => {
          // Truncate very long individual parts
          const maxPartLength = 500
          const displayValue = part.value.length > maxPartLength
            ? part.value.substring(0, maxPartLength) + '...'
            : part.value

          if (part.removed) {
            return (
              <span
                key={`removed-${index}`}
                className="bg-red-100 text-red-800 line-through decoration-red-500"
                style={{ textDecorationThickness: '2px' }}
              >
                {displayValue}
              </span>
            )
          } else if (part.added) {
            return (
              <span
                key={`added-${index}`}
                className="bg-green-100 text-green-800"
              >
                {displayValue}
              </span>
            )
          } else {
            return (
              <span key={`unchanged-${index}`} className="text-gray-700">
                {displayValue}
              </span>
            )
          }
        })}
        {diffParts.length > maxDiffParts && (
          <div className="text-gray-500 italic mt-2 p-2 bg-gray-50 rounded">
            ... and {diffParts.length - maxDiffParts} more changes (diff preview truncated for performance)
          </div>
        )}
      </div>
    )
  }, [diffParts])

  // Handle empty content and loading states
  if (!originalMarkdown && !initialValue) {
    return (
      <div className="text-gray-500 italic p-4 border border-gray-200 rounded-lg bg-gray-50">
        <div className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          No content to display
        </div>
      </div>
    )
  }

  // Handle very large content warning
  const isLargeContent = (originalMarkdown?.length || 0) > 20000 // 20KB
  if (isLargeContent && isEditMode) {
    console.warn('Large content detected, performance may be affected')
  }

  // Show loading overlay when processing
  if (isLoading) {
    return (
      <div className="relative">
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
          <div className="flex items-center gap-2 text-blue-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            Processing...
          </div>
        </div>
        <div className="opacity-50 pointer-events-none">
          {/* Render the component in disabled state */}
          <div className="text-gray-500 italic p-4">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`w-full enhanced-diff-editor ${className}`}>
      <AnimatePresence mode="wait">
        {!isEditMode ? (
          // View Mode
          <motion.div
            key="view-mode"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="space-y-4"
          >
            <div className="max-w-none text-gray-700">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  code({node, inline, className, children, ...props}: any) {
                    return !inline ? (
                      <pre className="bg-gray-300 text-black rounded-md p-3 overflow-x-auto my-3">
                        <code className={className} {...props}>
                          {children}
                        </code>
                      </pre>
                    ) : (
                      <code className="bg-gray-200 text-red-700 rounded px-1">{children}</code>
                    );
                  },
                  h1({children}) {
                    return (
                      <h1 className="text-2xl font-bold text-gray-900 mt-6 mb-4 pb-2 border-b border-gray-300">
                        {children}
                      </h1>
                    );
                  },
                  h2({children}) {
                    return (
                      <h2 className="text-xl font-semibold text-gray-900 mt-5 mb-3">
                        {children}
                      </h2>
                    );
                  },
                  h3({children}) {
                    return (
                      <h3 className="text-lg font-medium text-gray-900 mt-4 mb-2">
                        {children}
                      </h3>
                    );
                  },
                  p({children}) {
                    return (
                      <p className="my-3 leading-relaxed text-gray-700">
                        {children}
                      </p>
                    );
                  },
                  strong({children}) {
                    return (
                      <strong className="font-semibold text-gray-900">
                        {children}
                      </strong>
                    );
                  }
                }}
              >
                {originalMarkdown}
              </ReactMarkdown>
            </div>
            
            <div className="flex justify-start pt-2">
              <Button
                onClick={handleEditClick}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="h-4 w-4" />
                Edit
              </Button>
            </div>
          </motion.div>
        ) : (
          // Edit Mode
          <motion.div
            key="edit-mode"
            initial={{ x: '100%', opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: '100%', opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[400px]">
              {/* Left Pane - Editor */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Edit3 className="h-4 w-4" />
                  Editor
                </div>

                {/* Toolbar */}
                <div className="border border-gray-300 rounded-t-lg bg-gray-50 p-2 flex gap-1 flex-wrap">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => formatText('bold')}
                    className="h-8 w-8 p-0"
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => formatText('italic')}
                    className="h-8 w-8 p-0"
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => formatText('insertUnorderedList')}
                    className="h-8 w-8 p-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => formatText('insertOrderedList')}
                    className="h-8 w-8 p-0"
                  >
                    <ListOrdered className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => formatText('formatBlock', 'h2')}
                    className="h-8 px-2"
                  >
                    <Type className="h-4 w-4" />
                  </Button>
                </div>

                {/* Editor */}
                <div className="border border-gray-300 border-t-0 rounded-b-lg overflow-hidden">
                  <div
                    ref={editorRef}
                    contentEditable
                    className="min-h-[350px] p-4 bg-white focus:outline-none prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: editedContent }}
                    onInput={handleEditorInput}
                    onKeyDown={handleKeyDown}
                    onPaste={(e) => {
                      // Handle paste to maintain formatting
                      e.preventDefault()
                      const text = e.clipboardData.getData('text/plain')
                      document.execCommand('insertText', false, text)
                    }}
                    style={{
                      lineHeight: '1.6',
                      fontSize: '14px'
                    }}
                    suppressContentEditableWarning={true}
                    aria-label="Rich text editor"
                    role="textbox"
                    aria-multiline="true"
                  />
                </div>
              </div>

              {/* Right Pane - Diff Preview */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Eye className="h-4 w-4" />
                  Live Diff Preview
                </div>
                <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[350px] overflow-y-auto">
                  {renderDiffPreview()}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={isLoading}
                size="sm"
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                <Save className="h-4 w-4" />
                {isLoading ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Export memoized component for better performance
export const EnhancedDiffEditor = memo(EnhancedDiffEditorComponent)
