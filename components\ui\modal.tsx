import { ReactNode, useEffect, useState } from "react";

interface SideModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
}

export const SideModal = ({
  isOpen,
  onClose,
  title,
  children,
}: SideModalProps) => {
  const [shouldRender, setShouldRender] = useState(false);
  const [animateIn, setAnimateIn] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Delay ensures transition classes apply after DOM mounts
      setTimeout(() => setAnimateIn(true), 10);
      document.body.style.overflow = "hidden";
    } else {
      setAnimateIn(false);
      document.body.style.overflow = "unset";
      // Wait for transition to finish before removing from DOM
      const timeout = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timeout);
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex justify-end bg-black bg-opacity-50 transition-opacity duration-300 ${
        animateIn ? "opacity-100" : "opacity-0"
      }`}
      onClick={handleOverlayClick}
    >
      <div
        className={`h-full w-full w-[800px] bg-white shadow-lg transform transition-transform duration-300 ${
          animateIn ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex h-12 justify-between bg-[#3B4154] text-white p-5 items-center">
          <h2 className="text-lg font-semibold">{title || "Feedback"}</h2>
          <button
            onClick={onClose}
            className=" hover:bg-gray-500 text-xl py-1 px-2 rounded-md"
          >
            ✖
          </button>
        </div>
        <div className="p-4 overflow-y-auto h-full">{children}</div>
      </div>
    </div>
  );
};
